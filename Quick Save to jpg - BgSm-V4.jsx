/**
 * Quick Save to jpg - BgSm-V4
 * 
 * @version myVersion
 * <AUTHOR>
 * @date 2025-05-25
 * @description Quickly saves the current document as both large and small JPG and PNG files with timestamp
 * @license Copyright © 2025 Adam Frangione - All rights reserved
 */

#target photoshop;

var myVersion = "4.0";

var jpegQualityLarge = 12; // Default quality for large
var jpegQualitySmall = 6;  // Default quality for small
var useJpgFormat = true;   // Default to JPG format

// Variables for messages
var msg1 = "";
var msg2 = "";
var msg3 = "";
var msg4 = "Copyright © " + new Date().getFullYear() + " - <PERSON> (Version " + myVersion + ")";

// Add global variables to track format selection
var selectedFormat = "jpg"; // Default format: "jpg", "png", or "both"

// Add global variable for resize percentage
var smallResizePercent = 50; // Default to 50% size for small version

// Add global variable for progress dialog
var progressWin;

// Add global variable to track cancellation
var progressCancelled = false;

function showProgress(message, current, total) {
    // Create progress window if it doesn't exist
    if (!progressWin) {
        // Use a modeless palette window instead of a modal dialog
        progressWin = new Window('palette', 'Saving Progress');
        progressWin.orientation = 'column';
        progressWin.alignChildren = 'fill';
        
        // Add message text
        progressWin.msgText = progressWin.add('statictext', undefined, '');
        progressWin.msgText.preferredSize.width = 300;
        
        // Add progress bar
        progressWin.progressBar = progressWin.add('progressbar', undefined, 0, 100);
        progressWin.progressBar.preferredSize.width = 300;
        
        // Add cancel button
        var cancelBtn = progressWin.add('button', undefined, 'Cancel');
        cancelBtn.onClick = function() {
            progressCancelled = true;
            progressWin.close();
        };
        
        progressWin.center();
        progressWin.show();
    }
    
    // Check if operation was cancelled
    if (progressCancelled) {
        return false;
    }
    
    // Update progress information
    progressWin.msgText.text = message;
    var percent = Math.round((current / total) * 100);
    progressWin.progressBar.value = percent;
    
    // Force redraw to show progress updates
    progressWin.update();
    
    // Give the UI a chance to respond to user input
    app.refresh();
    $.sleep(10); // Small delay to allow UI interaction
    
    return true;
}

function closeProgress() {
    if (progressWin) {
        progressWin.close();
        progressWin = null;
    }
    // Reset cancelled flag
    progressCancelled = false;
}

//Test for open document
if(app.documents.length == 0){
    alert("Please open a project or create a new one before running this Script.");
} else {
    // Show dialog first to get user preferences
    showDialog();
}

function showDialog() {
    var window = new Window('dialog', 'Adam\'s QuickExport - V' + myVersion);
    window.orientation = "column";

    // Create Text Colors
    var winGraphics = window.graphics;  
    var red = winGraphics.newPen(winGraphics.BrushType.SOLID_COLOR, [1,0,0], 1);  
    var white = winGraphics.newPen(winGraphics.BrushType.SOLID_COLOR, [1,1,1], 1);  
    var gray = winGraphics.newPen(winGraphics.BrushType.SOLID_COLOR, [0.5,0.5,0.5], 1); 
    
    // Create Text Font and Size
    var arialBold24Font = ScriptUI.newFont("Arial", ScriptUI.FontStyle.BOLD, 24);
    var arialReg10Font = ScriptUI.newFont("Arial", ScriptUI.FontStyle.REGULAR, 10); 

    // Add JPEG quality settings
    var qualityGroup = window.add('panel', undefined, 'Quality Settings');
    qualityGroup.orientation = "row";

    var largeGroup = qualityGroup.add('group');
    largeGroup.orientation = "column";
    largeGroup.add('statictext', undefined, 'Large:');
    var largeSlider = largeGroup.add('slider', undefined, jpegQualityLarge, 1, 12);
    var largeValue = largeGroup.add('edittext', undefined, jpegQualityLarge);
    largeValue.characters = 2;

    var smallGroup = qualityGroup.add('group');
    smallGroup.orientation = "column";
    smallGroup.add('statictext', undefined, 'Small (JPG Only):');
    var smallSlider = smallGroup.add('slider', undefined, jpegQualitySmall, 1, 12);
    var smallValue = smallGroup.add('edittext', undefined, jpegQualitySmall);
    smallValue.characters = 2;

    // Connect sliders to text fields
    largeSlider.onChanging = function() {
      largeValue.text = Math.round(this.value);
      jpegQualityLarge = Math.round(this.value);
    };

    smallSlider.onChanging = function() {
      smallValue.text = Math.round(this.value);
      jpegQualitySmall = Math.round(this.value);
    };

    // Add format selection
    var formatGroup = window.add('panel', undefined, 'Format');
    formatGroup.orientation = "row";
    var jpgRB = formatGroup.add('radiobutton', undefined, 'JPG');
    var pngRB = formatGroup.add('radiobutton', undefined, 'PNG');
    var bothRB = formatGroup.add('radiobutton', undefined, 'Both');
    
    // Set default based on selectedFormat
    if (selectedFormat === "jpg") {
        jpgRB.value = true;
    } else if (selectedFormat === "png") {
        pngRB.value = true;
    } else if (selectedFormat === "both") {
        bothRB.value = true;
    }
    
    // Add resize percentage for small PNG version
    var resizeGroup = window.add('panel', undefined, 'Small Version Size - (PNG Only)');
    resizeGroup.orientation = "row";
    resizeGroup.add('statictext', undefined, 'Resize to:');
    var resizeSlider = resizeGroup.add('slider', undefined, smallResizePercent, 10, 90);
    var resizeValue = resizeGroup.add('edittext', undefined, smallResizePercent);
    resizeGroup.add('statictext', undefined, '% of original');
    resizeValue.characters = 3;
    
    // Connect resize slider to text field
    resizeSlider.onChanging = function() {
      resizeValue.text = Math.round(this.value);
      smallResizePercent = Math.round(this.value);
    };
    
    // Add buttons
    var buttonGroup = window.add('group');
    buttonGroup.orientation = "row";
    
    var saveBtn = buttonGroup.add('button', undefined, 'Save');
    var cancelBtn = buttonGroup.add('button', undefined, 'Cancel');
    
    saveBtn.onClick = function() {
        // Store format selection in global variable
        if (jpgRB.value) {
            selectedFormat = "jpg";
        } else if (pngRB.value) {
            selectedFormat = "png";
        } else if (bothRB.value) {
            selectedFormat = "both";
        }
        
        // Store resize percentage from text field
        smallResizePercent = parseInt(resizeValue.text, 10);
        if (isNaN(smallResizePercent) || smallResizePercent < 10 || smallResizePercent > 90) {
            smallResizePercent = 50; // Default if invalid
        }
        
        // Store quality settings (already handled by sliders)
        
        window.close();
        saveImages();
    };
    
    cancelBtn.onClick = function() {
        window.close();
    };
    
    window.center();
    window.show();
}

function saveImages() {
    try {
        // Reset cancellation flag at the start
        progressCancelled = false;
        
        var thedoc = app.activeDocument;
        var docName = thedoc.name;
        
        if (docName.indexOf(".") != -1) {
            var basename = docName.match(/(.*)\.[^\.]+$/)[1];
        } else {
            var basename = docName;
        }
        
        // Calculate total steps for progress
        var totalSteps = 0;
        if (selectedFormat === "jpg") totalSteps = 2; // Large and small JPG
        else if (selectedFormat === "png") totalSteps = 2; // Large and small PNG
        else if (selectedFormat === "both") totalSteps = 4; // Large and small of both formats
        
        var currentStep = 0;
        
        // Show initial progress
        if (!showProgress("Preparing to save files...", 0, totalSteps)) {
            return; // User cancelled
        }
        
        // Save large version
        currentStep++;
        if (!showProgress("Saving large version...", currentStep, totalSteps)) {
            closeProgress();
            return; // User cancelled
        }
        saveLargeVersion(thedoc, basename);
        
        // Check if cancelled during large version save
        if (progressCancelled) {
            closeProgress();
            return;
        }
        
        // Save small version
        currentStep++;
        if (!showProgress("Saving small version...", currentStep, totalSteps)) {
            closeProgress();
            return; // User cancelled
        }
        saveSmallVersion(thedoc, basename);
        
        // Close progress window
        closeProgress();
        
        // Only show completion message if not cancelled
        if (!progressCancelled) {
            showCompletionMessage();
        }
    } catch (e) {
        closeProgress();
        alert("Error in saveImages: " + e);
    }
}

function saveLargeVersion(thedoc, basename) {
    try {
        var docPath = thedoc.path;
        var docPathNew = new Folder(docPath+"/Edited");
        
        if (!docPathNew.exists) {
            if (!docPathNew.create()) {
                alert("Failed to create Edited folder. Check permissions.");
                // Fallback to desktop
                docPathNew = new Folder("~/Desktop/jpegExport");
                if (!docPathNew.exists) docPathNew.create();
            }
        }
        
        var docPath = docPathNew;
        msg1 = "Saved files in folder named";
        msg2 = " EDITED";
        msg3 = "Next to Original";
        
    } catch (e) {
        var docPath = "~/Desktop/jpegExport/";
        if (!new Folder(docPath).exists) {
            new Folder(docPath).create();
        }
        
        msg1 = "Saved files in folder named";
        msg2 = "jpegExport";
        msg3 = "on DESKTOP";
    }
    
    saveFile(docPath, basename, '_LARGE', true);
}

function saveSmallVersion(thedoc, basename) {
    try {
        var docPath = thedoc.path;
        var docPathNew = new Folder(docPath+"/Edited/Smalls");
        
        if (!docPathNew.exists) {
            if (!docPathNew.create()) {
                alert("Failed to create Edited/Smalls folder. Check permissions.");
                // Fallback to desktop
                docPathNew = new Folder("~/Desktop/jpegExport/Smalls");
                if (!docPathNew.exists) docPathNew.create();
            }
        }
        
        var docPath = docPathNew;
        
    } catch (e) {
        var docPath = "~/Desktop/jpegExport/Smalls";
        if (!new Folder(docPath).exists) {
            new Folder(docPath).create();
        }
    }
    
    saveFile(docPath, basename, '_SMALL', false);
}

function saveFile(docPath, basename, suffix, isLarge) {
    try {
        // Use the global selectedFormat variable
        var sizeType = isLarge ? "large" : "small";
        
        // Save as JPG if JPG format is selected or Both is selected
        if ((selectedFormat === "jpg" || selectedFormat === "both") && !progressCancelled) {
            if (!showProgress("Saving " + sizeType + " JPG file...", 0, 1)) {
                return; // User cancelled
            }
            
            var jpegOptions = new JPEGSaveOptions();
            jpegOptions.quality = isLarge ? jpegQualityLarge : jpegQualitySmall;
            jpegOptions.embedColorProfile = true;
            jpegOptions.matte = MatteType.NONE;
            
            var filename = docPath + '/' + basename + "-" + getTime() + suffix + '.jpg';
            
            // Give UI a chance to respond before heavy operation
            app.refresh();
            $.sleep(10);
            
            app.activeDocument.saveAs(new File(filename), jpegOptions, true);
            
            // Give UI a chance to respond after heavy operation
            app.refresh();
            $.sleep(10);
        }
        
        // Check if cancelled after JPG save
        if (progressCancelled) return;
        
        // Save as PNG if PNG format is selected or Both is selected
        if ((selectedFormat === "png" || selectedFormat === "both") && !progressCancelled) {
            if (!showProgress("Saving " + sizeType + " PNG file...", 0, 1)) {
                return; // User cancelled
            }
            
            var pngOptions = new PNGSaveOptions();
            pngOptions.compression = 9;
            pngOptions.interlaced = false;
            
            var filename = docPath + '/' + basename + "-" + getTime() + suffix + '.png';
            
            // For small version, resize the image before saving
            if (!isLarge) {
                if (!showProgress("Creating resized copy for small PNG...", 0.3, 1)) {
                    return; // User cancelled
                }
                
                // Give UI a chance to respond
                app.refresh();
                $.sleep(10);
                
                // Create a duplicate document for resizing
                var origDoc = app.activeDocument;
                var docCopy = origDoc.duplicate();
                
                if (progressCancelled) {
                    docCopy.close(SaveOptions.DONOTSAVECHANGES);
                    return;
                }
                
                if (!showProgress("Resizing small PNG...", 0.6, 1)) {
                    docCopy.close(SaveOptions.DONOTSAVECHANGES);
                    return; // User cancelled
                }
                
                // Give UI a chance to respond
                app.refresh();
                $.sleep(10);
                
                // Resize the duplicate based on smallResizePercent
                var resizeFactor = smallResizePercent / 100;
                docCopy.resizeImage(docCopy.width * resizeFactor, docCopy.height * resizeFactor);
                
                if (progressCancelled) {
                    docCopy.close(SaveOptions.DONOTSAVECHANGES);
                    return;
                }
                
                if (!showProgress("Saving small PNG...", 0.8, 1)) {
                    docCopy.close(SaveOptions.DONOTSAVECHANGES);
                    return; // User cancelled
                }
                
                // Give UI a chance to respond
                app.refresh();
                $.sleep(10);
                
                // Save the resized copy
                docCopy.saveAs(new File(filename), pngOptions, true);
                
                // Close the duplicate without saving
                docCopy.close(SaveOptions.DONOTSAVECHANGES);
            } else {
                // Give UI a chance to respond
                app.refresh();
                $.sleep(10);
                
                // Save the original size for large version
                app.activeDocument.saveAs(new File(filename), pngOptions, true);
            }
        }
    } catch (e) {
        closeProgress();
        alert("Error saving file: " + e);
    }
}

function showCompletionMessage() {
    try {
        var window = new Window('dialog', 'Adam\'s QuickExport - V' + myVersion);
        window.orientation = "column";

        // Create Text Colors
        var winGraphics = window.graphics;  
        var red = winGraphics.newPen(winGraphics.BrushType.SOLID_COLOR, [1,0,0], 1);  
        var white = winGraphics.newPen(winGraphics.BrushType.SOLID_COLOR, [1,1,1], 1);  
        var gray = winGraphics.newPen(winGraphics.BrushType.SOLID_COLOR, [0.5,0.5,0.5], 1); 
        
        // Create Text Font and Size
        var arialBold24Font = ScriptUI.newFont("Arial", ScriptUI.FontStyle.BOLD, 24);
        var arialReg10Font = ScriptUI.newFont("Arial", ScriptUI.FontStyle.REGULAR, 10); 

        // Show static text
        var label = window.add('statictext', undefined, msg1);
        var label2 = window.add('statictext', undefined, msg2);
        var label3 = window.add('statictext', undefined, msg3);
        var label4 = window.add('statictext', undefined, msg4);

        // Apply color 
        label.graphics.foregroundColor = white;   
        label2.graphics.foregroundColor = red;
        label3.graphics.foregroundColor = white;
        label4.graphics.foregroundColor = gray;  
     
        // Apply Font and Size 
        label2.graphics.font = arialBold24Font;
        label4.graphics.font = arialReg10Font; 

        // Add buttons group
        var buttonGroup = window.add('group');
        buttonGroup.orientation = "row";
        
        // Add open folder button
        var openFolderBtn = buttonGroup.add('button', undefined, 'Open Folder');
        openFolderBtn.preferredSize.width = 100;
        openFolderBtn.onClick = function() {
            openSaveLocation();
            window.close();
        }
        
        // Show close button
        var closeBtn = buttonGroup.add('button', undefined, 'Close');
        closeBtn.preferredSize.width = 100;
        closeBtn.onClick = function() {
            window.close();
        }

        window.center();
        window.show();
    } catch (e) {
        alert("Error in showCompletionMessage: " + e);
    }
}

function getTime() {
    var currentTime = new Date();
    
    // Format: YYYY-MM-DD_HH-MM-SS
    function pad(num) {
        return (num < 10 ? '0' : '') + num;
    }
    
    var timeStamp = currentTime.getFullYear() + "-" +
        pad(currentTime.getMonth() + 1) + "-" +
        pad(currentTime.getDate()) + "_" +
        pad(currentTime.getHours()) + "-" +
        pad(currentTime.getMinutes()) + "-" +
        pad(currentTime.getSeconds());
    
    return timeStamp;
}

// Add a new function to open the save location
function openSaveLocation() {
    try {
        // Get the path to open - use the large version folder
        var folderToOpen;
        
        try {
            var docPath = app.activeDocument.path;
            folderToOpen = new Folder(docPath + "/Edited");
            
            if (!folderToOpen.exists) {
                folderToOpen = new Folder("~/Desktop/jpegExport");
            }
        } catch (e) {
            folderToOpen = new Folder("~/Desktop/jpegExport");
        }
        
        // Open the folder in Explorer/Finder
        folderToOpen.execute();
    } catch (e) {
        alert("Could not open folder: " + e);
    }
}

